@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.But<PERSON>
@inherits AlertDialogBase

<SfDialog @bind-Visible="IsVisible" Header="@Title" Width="400px" Height="auto" IsModal="true"
          ShowCloseIcon="true" AllowDragging="false" CssClass="alert-dialog">
    <DialogEvents Closed="OnDialogClosed"></DialogEvents>
    <DialogTemplates>
        <Content>
            <div class="alert-content">
                <div class="d-flex align-items-start mb-3">
                    <i class="@GetIconClass() me-3" style="font-size: 2rem;"></i>
                    <div>
                        <p class="mb-2 fw-semibold">@Message</p>
                        @if (!string.IsNullOrEmpty(Details))
                        {
                            <p class="text-muted small mb-0">@Details</p>
                        }
                    </div>
                </div>
            </div>
        </Content>
    </DialogTemplates>
    <DialogButtons>
        <DialogButton OnClick="OnOkClick" Content="@OkButtonText" CssClass="@GetButtonClass()" IsPrimary="true"></DialogButton>
    </DialogButtons>
</SfDialog>
