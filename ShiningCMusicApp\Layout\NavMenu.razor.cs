using Microsoft.AspNetCore.Components;
using ShiningCMusicApp.Services;

namespace ShiningCMusicApp.Layout
{
    public partial class NavMenu : ComponentBase
    {
        [Inject] protected NavigationManager Navigation { get; set; } = default!;
        [Inject] protected CustomAuthenticationStateProvider AuthStateProvider { get; set; } = default!;

        private bool collapseNavMenu = true;
        protected string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

        protected void ToggleNavMenu()
        {
            collapseNavMenu = !collapseNavMenu;
        }

        protected async Task Logout()
        {
            await AuthStateProvider.LogoutAsync();
            Navigation.NavigateTo("/login");
        }
    }
}
