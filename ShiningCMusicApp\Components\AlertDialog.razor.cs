using Microsoft.AspNetCore.Components;
using ShiningCMusicApp.Services.Interfaces;

namespace ShiningCMusicApp.Components
{
    public partial class AlertDialogBase : ComponentBase, IDisposable
    {
        [Inject] protected IDialogService DialogService { get; set; } = default!;

        [Parameter] public bool IsVisible { get; set; }
        [Parameter] public EventCallback<bool> IsVisibleChanged { get; set; }
        public string Title { get; set; } = "Alert";
        public string Message { get; set; } = "Alert message";
        public string? Details { get; set; }
        public string OkButtonText { get; set; } = "OK";
        public AlertType Type { get; set; } = AlertType.Info;
        [Parameter] public EventCallback OnResult { get; set; }

        private TaskCompletionSource<bool>? _taskCompletionSource;

        protected override void OnInitialized()
        {
            if (DialogService is ShiningCMusicApp.Services.DialogService service)
            {
                service.RegisterAlertDialog(this);
            }
        }

        public Task ShowAsync()
        {
            _taskCompletionSource = new TaskCompletionSource<bool>();
            IsVisible = true;
            InvokeAsync(StateHasChanged);
            return _taskCompletionSource.Task;
        }

        protected async Task OnOkClick()
        {
            IsVisible = false;
            await IsVisibleChanged.InvokeAsync(false);
            await OnResult.InvokeAsync();
            _taskCompletionSource?.SetResult(true);
        }

        protected async Task OnDialogClosed()
        {
            IsVisible = false;
            await IsVisibleChanged.InvokeAsync(false);
            await OnResult.InvokeAsync();
            _taskCompletionSource?.SetResult(true);
        }

        protected string GetIconClass()
        {
            return Type switch
            {
                AlertType.Success => "bi bi-check-circle-fill text-success",
                AlertType.Warning => "bi bi-exclamation-triangle-fill text-warning",
                AlertType.Error => "bi bi-x-circle-fill text-danger",
                AlertType.Info => "bi bi-info-circle-fill text-info",
                _ => "bi bi-info-circle-fill text-info"
            };
        }

        protected string GetButtonClass()
        {
            return Type switch
            {
                AlertType.Success => "btn btn-success",
                AlertType.Warning => "btn btn-warning",
                AlertType.Error => "btn btn-danger",
                AlertType.Info => "btn btn-blue-custom",
                _ => "btn btn-blue-custom"
            };
        }

        public void Dispose()
        {
            if (DialogService is ShiningCMusicApp.Services.DialogService service)
            {
                service.UnregisterAlertDialog(this);
            }
        }
    }
}
