using Microsoft.AspNetCore.Components;
using ShiningCMusicApp.Services;

namespace ShiningCMusicApp.Layout
{
    public partial class MainLayout : LayoutComponentBase
    {
        [Inject] protected CustomAuthenticationStateProvider AuthStateProvider { get; set; } = default!;
        [Inject] protected NavigationManager Navigation { get; set; } = default!;

        protected async Task Logout()
        {
            await AuthStateProvider.LogoutAsync();
            Navigation.NavigateTo("/login");
        }

        protected string GetShortName(string? fullName)
        {
            if (string.IsNullOrEmpty(fullName))
                return "";

            var parts = fullName.Split(' ');
            if (parts.Length > 1)
                return $"{parts[0][0]}.{parts[^1]}"; // First initial + last name

            return fullName.Length > 10 ? fullName[..10] + "..." : fullName;
        }
    }
}
