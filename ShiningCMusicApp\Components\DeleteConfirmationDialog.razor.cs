using Microsoft.AspNetCore.Components;
using ShiningCMusicApp.Services.Interfaces;

namespace ShiningCMusicApp.Components
{
    public partial class DeleteConfirmationDialogBase : ComponentBase, IDisposable
    {
        [Inject] protected IDialogService DialogService { get; set; } = default!;

        [Parameter] public bool IsVisible { get; set; }
        [Parameter] public EventCallback<bool> IsVisibleChanged { get; set; }
        public string Title { get; set; } = "Confirm Delete";
        public string Message { get; set; } = "Are you sure you want to delete this item?";
        public string? Details { get; set; }
        public string ConfirmButtonText { get; set; } = "Delete";
        public string CancelButtonText { get; set; } = "Cancel";
        [Parameter] public EventCallback<bool> OnResult { get; set; }

        private TaskCompletionSource<bool>? _taskCompletionSource;

        protected override void OnInitialized()
        {
            if (DialogService is ShiningCMusicApp.Services.DialogService service)
            {
                service.RegisterDialog(this);
            }
        }

        public Task<bool> ShowAsync()
        {
            _taskCompletionSource = new TaskCompletionSource<bool>();
            IsVisible = true;
            InvokeAsync(StateHasChanged);
            return _taskCompletionSource.Task;
        }

        protected async Task OnConfirmClick()
        {
            IsVisible = false;
            await IsVisibleChanged.InvokeAsync(false);
            await OnResult.InvokeAsync(true);
            _taskCompletionSource?.SetResult(true);
        }

        protected async Task OnCancelClick()
        {
            IsVisible = false;
            await IsVisibleChanged.InvokeAsync(false);
            await OnResult.InvokeAsync(false);
            _taskCompletionSource?.SetResult(false);
        }

        protected async Task OnDialogClosed()
        {
            IsVisible = false;
            await IsVisibleChanged.InvokeAsync(false);
            await OnResult.InvokeAsync(false);
            _taskCompletionSource?.SetResult(false);
        }

        public void Dispose()
        {
            if (DialogService is ShiningCMusicApp.Services.DialogService service)
            {
                service.UnregisterDialog(this);
            }
        }
    }
}
